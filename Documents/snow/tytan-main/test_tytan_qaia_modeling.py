#!/usr/bin/env python3
"""
Test script for the Tytan QAIA modeling implementation.

This script tests the conversion from PyQUBO to Tytan QAIA modeling
and verifies that the optimization works correctly.
"""

import sys
import numpy as np
from tytan_qaia_modeling import (
    use_annealing_tytan, 
    create_sample_input, 
    post_processing_tytan
)


def test_basic_functionality():
    """Test basic functionality of the Tytan QAIA modeling."""
    print("=== Testing Basic Functionality ===\n")
    
    # Create simple test case
    junc_info = {
        0: {'num_modes': 2},  # Junction 0: 2 modes
        1: {'num_modes': 2},  # Junction 1: 2 modes
    }
    
    # Simple cost matrix - prefer mode 0 for both junctions
    C = {
        (0, 0): 1.0,  # Low cost for junction 0, mode 0
        (0, 1): 5.0,  # High cost for junction 0, mode 1
        (1, 0): 2.0,  # Low cost for junction 1, mode 0
        (1, 1): 6.0,  # High cost for junction 1, mode 1
    }
    
    # No interactions for simplicity
    BR = {}
    
    # Parameters
    alpha = 1.0
    beta = 0.0  # No interaction term
    gamma = 10.0
    num_reads = 20
    
    print("Test case:")
    print(f"- Junctions: {list(junc_info.keys())}")
    print(f"- Costs: {C}")
    print(f"- Expected solution: Junction 0 -> mode 0, Junction 1 -> mode 0")
    print()
    
    try:
        solution, results, elapsed_time = use_annealing_tytan(
            C, BR, alpha, beta, gamma, num_reads, junc_info,
            algorithm='asb', backend='cpu-float32', seed=42
        )
        
        print("✓ Optimization completed successfully!")
        print(f"  Time: {elapsed_time:.3f} seconds")
        print(f"  Best energy: {results[0][1]:.4f}")
        print(f"  Solution: {solution}")
        
        # Verify constraint satisfaction
        constraint_satisfied = True
        for i in junc_info.keys():
            mode_sum = sum(solution[i].values())
            if abs(mode_sum - 1.0) > 1e-6:
                constraint_satisfied = False
                print(f"  ✗ Constraint violation for junction {i}: sum = {mode_sum}")
        
        if constraint_satisfied:
            print("  ✓ All constraints satisfied")
        
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        return False


def test_constraint_enforcement():
    """Test that constraints are properly enforced."""
    print("\n=== Testing Constraint Enforcement ===\n")
    
    # Create a case where constraint enforcement is needed
    junc_info = {
        0: {'num_modes': 3},
    }
    
    # Test post-processing function
    test_solution = {
        0: {0: 0.7, 1: 0.3, 2: 0.1}  # Multiple modes partially selected
    }
    
    processed = post_processing_tytan(test_solution, junc_info)
    
    print("Before post-processing:", test_solution)
    print("After post-processing:", processed)
    
    # Verify exactly one mode is selected
    mode_sum = sum(processed[0].values())
    if abs(mode_sum - 1.0) < 1e-6:
        print("✓ Constraint enforcement works correctly")
        return True
    else:
        print(f"✗ Constraint enforcement failed: sum = {mode_sum}")
        return False


def test_sample_input_generation():
    """Test the sample input generation function."""
    print("\n=== Testing Sample Input Generation ===\n")
    
    try:
        C, BR, alpha, beta, gamma, num_reads, junc_info = create_sample_input()
        
        print("Generated sample input:")
        print(f"- Junctions: {list(junc_info.keys())}")
        print(f"- Total modes: {sum(junc_info[i]['num_modes'] for i in junc_info.keys())}")
        print(f"- C matrix size: {len(C)}")
        print(f"- BR matrix size: {len(BR)}")
        print(f"- Parameters: α={alpha}, β={beta}, γ={gamma}")
        
        # Verify data consistency
        expected_c_size = sum(junc_info[i]['num_modes'] for i in junc_info.keys())
        if len(C) == expected_c_size:
            print("✓ C matrix size is correct")
        else:
            print(f"✗ C matrix size mismatch: expected {expected_c_size}, got {len(C)}")
            return False
        
        # Test with the generated input
        print("\nTesting optimization with generated input...")
        solution, results, elapsed_time = use_annealing_tytan(
            C, BR, alpha, beta, gamma, 10, junc_info,  # Reduced num_reads for speed
            algorithm='asb', backend='cpu-float32', seed=42
        )
        
        print(f"✓ Optimization with generated input successful!")
        print(f"  Energy: {results[0][1]:.4f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Sample input generation test failed: {e}")
        return False


def test_algorithm_comparison():
    """Test different QAIA algorithms."""
    print("\n=== Testing Algorithm Comparison ===\n")
    
    # Use simple input for faster testing
    C, BR, alpha, beta, gamma, num_reads, junc_info = create_sample_input()
    num_reads = 10  # Reduced for speed
    
    algorithms = ['asb', 'bsb', 'dsb']
    results = {}
    
    for algorithm in algorithms:
        print(f"Testing {algorithm.upper()}...")
        
        try:
            solution, all_results, elapsed_time = use_annealing_tytan(
                C, BR, alpha, beta, gamma, num_reads, junc_info,
                algorithm=algorithm, backend='cpu-float32', seed=42
            )
            
            results[algorithm] = {
                'energy': all_results[0][1],
                'time': elapsed_time,
                'success': True
            }
            
            print(f"  ✓ {algorithm.upper()}: Energy = {all_results[0][1]:.4f}, Time = {elapsed_time:.3f}s")
            
        except Exception as e:
            results[algorithm] = {'success': False, 'error': str(e)}
            print(f"  ✗ {algorithm.upper()}: {e}")
    
    successful_algorithms = [k for k, v in results.items() if v.get('success', False)]
    
    if successful_algorithms:
        print(f"\n✓ {len(successful_algorithms)}/{len(algorithms)} algorithms succeeded")
        return True
    else:
        print(f"\n✗ No algorithms succeeded")
        return False


def run_all_tests():
    """Run all tests and report results."""
    print("Starting Tytan QAIA Modeling Tests...\n")
    
    tests = [
        ("Basic Functionality", test_basic_functionality),
        ("Constraint Enforcement", test_constraint_enforcement),
        ("Sample Input Generation", test_sample_input_generation),
        ("Algorithm Comparison", test_algorithm_comparison),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✓ PASS" if success else "✗ FAIL"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
