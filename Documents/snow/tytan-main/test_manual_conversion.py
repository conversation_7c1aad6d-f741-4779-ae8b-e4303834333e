#!/usr/bin/env python3
"""
Test manual QUBO to Ising conversion using the exact D-Wave formula.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

from tytan import symbols, Compile, clear_symbol_registry
import numpy as np


def manual_qubo_to_ising(Q):
    """
    Manual QUBO to Ising conversion using D-Wave formula.
    
    For QUBO: minimize x^T Q x where x ∈ {0,1}
    For Ising: minimize s^T J s + h^T s + constant where s ∈ {-1,+1}
    Using x = (s + 1)/2
    """
    n = Q.shape[0]
    
    # Make Q symmetric
    Q_sym = Q + Q.T
    for i in range(n):
        Q_sym[i, i] = Q[i, i]
    
    # D-Wave formula:
    J = np.zeros((n, n))
    h = np.zeros(n)
    
    # J_ij = Q_ij / 4 for i ≠ j
    for i in range(n):
        for j in range(n):
            if i != j:
                J[i, j] = Q_sym[i, j] / 4
    
    # h_i = (Q_ii + sum_j≠i Q_ij) / 2
    for i in range(n):
        h[i] = Q_sym[i, i] / 2
        for j in range(n):
            if i != j:
                h[i] += Q_sym[i, j] / 4
    
    # constant = sum_i Q_ii / 4 + sum_i sum_j>i Q_ij / 4
    constant = 0
    for i in range(n):
        constant += Q_sym[i, i] / 4
    for i in range(n):
        for j in range(i+1, n):
            constant += Q_sym[i, j] / 4
    
    return J, h, constant


def test_conversion():
    """Test the conversion with our problem."""
    print("Manual QUBO to Ising conversion test")
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print("QUBO matrix:")
    print(matrix)
    print("Offset:", offset)
    
    # Manual conversion
    J, h, constant = manual_qubo_to_ising(matrix)
    
    print("\nManual conversion:")
    print("J matrix:")
    print(J)
    print("h vector:", h)
    print("constant:", constant)
    
    # Test with solution [1,1,0]
    binary_sol = np.array([1, 1, 0])
    qubo_energy = binary_sol.T @ matrix @ binary_sol
    
    ising_sol = 2 * binary_sol - 1  # [1, 1, -1]
    ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + constant
    
    print(f"\nTesting solution [1,1,0] -> Ising [1,1,-1]:")
    print(f"QUBO energy: {qubo_energy}")
    print(f"Ising energy: {ising_energy}")
    print(f"Match: {abs(qubo_energy - ising_energy) < 1e-10}")
    
    # Let's also manually calculate the Ising energy step by step
    print(f"\nManual Ising calculation:")
    print(f"s = {ising_sol}")
    print(f"J @ s = {J @ ising_sol}")
    print(f"s^T @ J @ s = {ising_sol.T @ J @ ising_sol}")
    print(f"h^T @ s = {h.T @ ising_sol}")
    print(f"constant = {constant}")
    print(f"Total = {ising_sol.T @ J @ ising_sol} + {h.T @ ising_sol} + {constant} = {ising_energy}")


if __name__ == "__main__":
    test_conversion()
