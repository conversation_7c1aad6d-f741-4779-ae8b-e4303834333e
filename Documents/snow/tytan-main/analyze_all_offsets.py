#!/usr/bin/env python3
"""
Comprehensive analysis of QUBO offset, Ising offset, and energy differences.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

import numpy as np
from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def analyze_complete_energy_flow():
    """
    Analyze the complete energy flow from original expression to final energies.
    """
    print("🔍 COMPLETE ENERGY FLOW ANALYSIS")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    # Get QUBO with offset
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    # Get Ising conversion with offset
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"Original expression: (x + y + z - 2)²")
    print(f"")
    print(f"📊 OFFSET SUMMARY:")
    print(f"QUBO offset:     {qubo_offset}")
    print(f"Ising offset:    {ising_offset}")
    print(f"Total offset:    {qubo_offset + ising_offset}")
    
    print(f"\n🔄 ENERGY FORMULATIONS:")
    print(f"1. Original:     (x + y + z - 2)²")
    print(f"2. QUBO:         x^T Q x + {qubo_offset}")
    print(f"3. Ising:        s^T J s + h^T s + {ising_offset}")
    
    # Test with all solutions
    print(f"\n📋 COMPLETE ENERGY COMPARISON:")
    print(f"{'Binary':<12} {'Original':<10} {'QUBO Raw':<10} {'QUBO+Off':<10} {'Ising Raw':<11} {'Ising+Off':<11} {'Final':<10}")
    print(f"{'='*85}")
    
    all_solutions = [
        [0, 0, 0], [0, 0, 1], [0, 1, 0], [0, 1, 1],
        [1, 0, 0], [1, 0, 1], [1, 1, 0], [1, 1, 1]
    ]
    
    for binary_sol in all_solutions:
        binary_sol = np.array(binary_sol)
        ising_sol = 2 * binary_sol - 1
        
        # Calculate all energy types
        original_energy = (sum(binary_sol) - 2)**2
        qubo_raw = binary_sol.T @ matrix @ binary_sol
        qubo_total = qubo_raw + qubo_offset
        ising_raw = ising_sol.T @ J @ ising_sol + h.T @ ising_sol
        ising_total = ising_raw + ising_offset
        final_energy = ising_raw + qubo_offset  # What we actually use
        
        print(f"{str(list(binary_sol)):<12} {original_energy:<10.1f} {qubo_raw:<10.1f} {qubo_total:<10.1f} "
              f"{ising_raw:<11.1f} {ising_total:<11.1f} {final_energy:<10.1f}")
    
    return qubo_offset, ising_offset


def answer_offset_questions():
    """
    Answer the specific questions about QUBO offset and energy differences.
    """
    print(f"\n" + "=" * 60)
    print(f"🎯 ANSWERING YOUR QUESTIONS")
    print(f"=" * 60)
    
    qubo_offset, ising_offset = analyze_complete_energy_flow()
    
    print(f"\n❓ QUESTION 1: Does QUBO model also have an offset?")
    print(f"✅ YES! QUBO models have an offset.")
    print(f"   - QUBO offset: {qubo_offset}")
    print(f"   - This comes from the constant term in the original expression")
    print(f"   - Example: (x + y + z - 2)² = ... + 4")
    print(f"                                      ↑")
    print(f"                               QUBO offset")
    
    print(f"\n❓ QUESTION 2: What is the energy difference between QUBO and Ising?")
    print(f"📊 There are multiple ways to compare energies:")
    
    # Test with one solution for detailed explanation
    binary_sol = np.array([1, 1, 0])
    ising_sol = 2 * binary_sol - 1
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    original_energy = (sum(binary_sol) - 2)**2
    qubo_raw = binary_sol.T @ matrix @ binary_sol
    qubo_total = qubo_raw + qubo_offset
    ising_raw = ising_sol.T @ J @ ising_sol + h.T @ ising_sol
    ising_total = ising_raw + ising_offset
    
    print(f"\n📋 For solution [1,1,0]:")
    print(f"Original energy:           {original_energy}")
    print(f"QUBO raw energy:           {qubo_raw}")
    print(f"QUBO total energy:         {qubo_total}")
    print(f"Ising raw energy:          {ising_raw}")
    print(f"Ising total energy:        {ising_total}")
    
    print(f"\n🔢 ENERGY DIFFERENCES:")
    print(f"1. QUBO raw vs Ising raw:     {qubo_raw} - ({ising_raw}) = {qubo_raw - ising_raw}")
    print(f"2. QUBO total vs Ising total: {qubo_total} - ({ising_total}) = {qubo_total - ising_total}")
    print(f"3. Offset difference:         {qubo_offset} - ({ising_offset}) = {qubo_offset - ising_offset}")


def explain_offset_relationships():
    """
    Explain the mathematical relationships between offsets.
    """
    print(f"\n" + "=" * 60)
    print(f"🧮 MATHEMATICAL RELATIONSHIPS")
    print(f"=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"🔄 The complete energy equivalence chain:")
    print(f"")
    print(f"Original Expression Energy:")
    print(f"  E_original = (x + y + z - 2)²")
    print(f"")
    print(f"QUBO Formulation:")
    print(f"  E_qubo_total = x^T Q x + qubo_offset")
    print(f"  E_qubo_total = {matrix[0,0]}x² + {matrix[1,1]}y² + {matrix[2,2]}z² + {matrix[0,1]}xy + {matrix[0,2]}xz + {matrix[1,2]}yz + {qubo_offset}")
    print(f"")
    print(f"Ising Formulation:")
    print(f"  E_ising_total = s^T J s + h^T s + ising_offset")
    print(f"  E_ising_total = Ising_raw + {ising_offset}")
    
    print(f"\n✅ KEY RELATIONSHIPS:")
    print(f"1. E_original = E_qubo_total")
    print(f"2. E_qubo_raw = E_ising_raw + ising_offset")
    print(f"3. E_qubo_total = E_qubo_raw + qubo_offset")
    print(f"4. E_ising_total = E_ising_raw + ising_offset")
    
    print(f"\n🎯 PRACTICAL ENERGY CALCULATION:")
    print(f"In optimization, we typically use:")
    print(f"  Final_energy = Ising_raw_energy + QUBO_offset")
    print(f"  Final_energy = (s^T J s + h^T s) + {qubo_offset}")
    print(f"")
    print(f"This gives us the energy in the original problem scale!")


def demonstrate_energy_consistency():
    """
    Demonstrate that all energy formulations are consistent.
    """
    print(f"\n" + "=" * 60)
    print(f"✅ ENERGY CONSISTENCY VERIFICATION")
    print(f"=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"Testing energy consistency across all formulations...")
    print(f"")
    
    # Test a few solutions
    test_solutions = [[0,0,0], [1,1,0], [0,1,1], [1,1,1]]
    
    for binary_sol in test_solutions:
        binary_sol = np.array(binary_sol)
        ising_sol = 2 * binary_sol - 1
        
        # Calculate using all methods
        original = (sum(binary_sol) - 2)**2
        qubo_raw = binary_sol.T @ matrix @ binary_sol
        qubo_total = qubo_raw + qubo_offset
        ising_raw = ising_sol.T @ J @ ising_sol + h.T @ ising_sol
        ising_total = ising_raw + ising_offset
        practical = ising_raw + qubo_offset
        
        # Check consistency
        original_qubo_match = abs(original - qubo_total) < 1e-10
        qubo_ising_match = abs(qubo_raw - (ising_raw + ising_offset)) < 1e-10
        
        print(f"Solution {list(binary_sol)}:")
        print(f"  Original:     {original:6.1f}")
        print(f"  QUBO total:   {qubo_total:6.1f}  {'✓' if original_qubo_match else '✗'}")
        print(f"  Ising total:  {ising_total:6.1f}")
        print(f"  Practical:    {practical:6.1f}")
        print(f"  Consistency:  {'✓' if qubo_ising_match else '✗'}")
        print()


def main():
    """
    Complete analysis of QUBO and Ising offsets and energy differences.
    """
    print("🔬 COMPLETE OFFSET AND ENERGY ANALYSIS")
    print("Understanding QUBO offset, Ising offset, and energy differences")
    
    try:
        analyze_complete_energy_flow()
        answer_offset_questions()
        explain_offset_relationships()
        demonstrate_energy_consistency()
        
        print("=" * 60)
        print("🎯 FINAL SUMMARY")
        print("=" * 60)
        print("✅ YES - QUBO models have an offset (from original expression)")
        print("✅ Ising models have an offset (from QUBO→Ising conversion)")
        print("✅ Energy difference = ising_offset (constant for all solutions)")
        print("✅ All formulations are mathematically consistent")
        print("✅ For optimization: use Ising_raw + QUBO_offset")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    main()
