# QAIASampler Documentation

## Overview

The `QAIASampler` class integrates the QAIA (Quantum Annealing Inspired Algorithm) child classes into the TYTAN framework, providing a unified interface for solving QUBO (Quadratic Unconstrained Binary Optimization) problems using quantum-inspired optimization algorithms.

## Key Features

- **Automatic QUBO to Ising Conversion**: Seamlessly converts QUBO problems to Ising model format required by QAIA algorithms
- **Multiple Algorithm Support**: Access to ASB, BSB, and DSB algorithms
- **Consistent Output Format**: Uses the same output schema as other TYTAN samplers
- **Proper Solution Extraction**: Applies `torch.sign()` to raw outputs for discrete Ising solutions
- **Type Consistency**: Ensures output symbols match the input expression types

## Installation Requirements

- Python 3.7+
- NumPy
- PyTorch (for GPU backends)
- TYTAN library with QAIA algorithms

## Basic Usage

```python
from tytan import symbols, Compile
from tytan.qaia_sampler import QAIASampler

# Create optimization problem
x, y = symbols('x y')
expr = (x + y - 1)**2  # Constraint: exactly one variable should be 1

# Compile to QUBO
qubo, offset = Compile(expr).get_qubo()

# Initialize sampler
sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')

# Solve
result = sampler.run(qubo, shots=100, n_iter=1000)

# Access best solution
best_solution = result[0][0]  # Dictionary: {'x': 0, 'y': 1}
best_energy = result[0][1]    # Energy value
count = result[0][2]          # Number of occurrences
```

## Constructor Parameters

### QAIASampler(algorithm='asb', seed=None, backend='cpu-float32', **kwargs)

- **algorithm** (str): QAIA algorithm to use
  - `'asb'`: Adiabatic Simulated Bifurcation
  - `'bsb'`: Ballistic Simulated Bifurcation  
  - `'dsb'`: Discrete Simulated Bifurcation
- **seed** (int, optional): Random seed for reproducibility
- **backend** (str): Computation backend
  - `'cpu-float32'`: CPU computation (default)
  - `'gpu-float32'`: GPU computation (requires PyTorch + CUDA)
  - `'npu-float32'`: NPU computation (requires torch_npu)
- **kwargs**: Additional parameters passed to the QAIA algorithm

## Run Method Parameters

### sampler.run(qubomix, shots=100, n_iter=1000, **run_kwargs)

- **qubomix**: Tuple of (QUBO matrix, index mapping) from `Compile().get_qubo()`
- **shots** (int): Number of samples to generate (default: 100)
- **n_iter** (int): Number of algorithm iterations (default: 1000)
- **run_kwargs**: Algorithm-specific parameters:
  - **dt** (float): Time step size (smaller = more stable, default: 1.0)
  - **xi** (float): Coupling strength (smaller = more stable, auto-calculated if None)
  - **M** (int): Update steps without mean-field terms (ASB only, default: 2)

## Output Format

Returns a list of solutions sorted by energy (lowest first):

```python
[
    [{'x': 0, 'y': 1}, -1.0, 5],  # Solution 1: variables, energy, count
    [{'x': 1, 'y': 0}, -1.0, 3],  # Solution 2: variables, energy, count
    # ... more solutions
]
```

## Algorithm Comparison

| Algorithm | Description | Best For |
|-----------|-------------|----------|
| **ASB** | Adiabatic Simulated Bifurcation | General problems, good balance |
| **BSB** | Ballistic Simulated Bifurcation | Fast convergence, sparse problems |
| **DSB** | Discrete Simulated Bifurcation | Discrete optimization, binary constraints |

## Parameter Tuning Guidelines

### For Stability (Avoiding Overflow)
- Reduce `dt` (e.g., 0.1 instead of 1.0)
- Reduce `xi` (e.g., 0.1 instead of auto-calculated)
- Reduce `n_iter` for initial testing

### For Performance
- Increase `shots` for more diverse solutions
- Increase `n_iter` for better convergence
- Use GPU backend for larger problems

## Examples

### Simple Constraint Problem
```python
# Exactly 2 out of 3 variables should be 1
x1, x2, x3 = symbols('x1 x2 x3')
expr = (x1 + x2 + x3 - 2)**2

qubo, offset = Compile(expr).get_qubo()
sampler = QAIASampler(algorithm='asb')
result = sampler.run(qubo, shots=50, n_iter=200)
```

### Max-Cut Problem
```python
# Graph partitioning to maximize cut
x1, x2, x3, x4 = symbols('x1 x2 x3 x4')
edges = [(x1, x2), (x2, x3), (x3, x4), (x4, x1)]
expr = sum(2*xi*xj - xi - xj + 1 for xi, xj in edges)

qubo, offset = Compile(expr).get_qubo()
sampler = QAIASampler(algorithm='bsb')
result = sampler.run(qubo, shots=100, n_iter=500)
```

### Algorithm Comparison
```python
algorithms = ['asb', 'bsb', 'dsb']
results = {}

for algo in algorithms:
    sampler = QAIASampler(algorithm=algo, seed=42)
    result = sampler.run(qubo, shots=20, n_iter=100)
    results[algo] = result[0][1]  # Best energy

best_algo = min(results.keys(), key=lambda k: results[k])
print(f"Best algorithm: {best_algo}")
```

## Error Handling

Common issues and solutions:

### OverflowException
```
RuntimeError: QAIA algorithm execution failed: Value is too large to handle due to large dt or xi.
```
**Solution**: Reduce `dt` and/or `xi` parameters:
```python
result = sampler.run(qubo, dt=0.1, xi=0.1)
```

### GPU Backend Issues
```
ImportError: PyTorch is required for GPU backends.
```
**Solution**: Install PyTorch or use CPU backend:
```python
sampler = QAIASampler(backend='cpu-float32')
```

## Testing

Run the test suite:
```bash
conda activate kag-demo-latest
python -m pytest tests/sampler/test_qaia_sampler.py -v
```

Run demonstrations:
```bash
python demo_qaia_sampler.py
python example_qaia_usage.py
```

## Technical Details

### QUBO to Ising Conversion
The sampler automatically converts QUBO problems to Ising format using:
- **Transformation**: x = (s + 1)/2, where x ∈ {0,1} and s ∈ {-1,+1}
- **Energy mapping**: Preserves optimization landscape
- **Reverse conversion**: s → x for final binary solutions

### Solution Processing
1. Raw QAIA output → `torch.sign()` → Discrete Ising solutions
2. Ising solutions → Binary transformation → QUBO-compatible results
3. Energy calculation using original QUBO matrix
4. Deduplication and sorting via `get_result()`

## Integration with TYTAN

The QAIASampler follows TYTAN's sampler conventions:
- Same input format as other samplers (QUBO matrix + index map)
- Same output format as `get_result()` function
- Compatible with existing TYTAN workflows
- Supports all TYTAN symbol types

## Performance Notes

- **CPU Backend**: Suitable for problems up to ~100 variables
- **GPU Backend**: Recommended for larger problems (requires CUDA)
- **Memory Usage**: Scales with `shots × n_variables`
- **Convergence**: Monitor energy trends; increase `n_iter` if needed

## Contributing

To extend the QAIASampler:
1. Add new algorithms to the algorithm selection in `run()`
2. Update parameter validation and documentation
3. Add corresponding tests in `test_qaia_sampler.py`
4. Update this README with new features
