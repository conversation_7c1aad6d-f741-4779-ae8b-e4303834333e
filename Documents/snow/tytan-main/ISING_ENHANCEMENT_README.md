# Enhanced Symbol Creation with Ising Support

This document describes the enhanced symbol creation functionality that has been added to the TYTAN library, providing support for Ising model formulations and automatic conversion to QUBO format.

## Overview

The TYTAN library has been enhanced with the following new capabilities:

1. **Symbol Type Declaration**: Ability to declare symbols as either "binary" (0,1) or "ising" (-1,+1)
2. **Automatic Ising to QUBO Conversion**: When Ising symbols are used, the `get_qubo()` method automatically converts them to QUBO format
3. **Mixed Variable Support**: Support for expressions containing both binary and Ising variables
4. **Backward Compatibility**: All existing code continues to work without modification

## New Features

### 1. Symbol Type Declaration

You can now specify the type of symbols when creating them:

```python
from tytan import symbols, symbols_list

# Create Ising symbols (values: -1, +1)
s1, s2 = symbols('s1 s2', symbol_type="ising")

# Create binary symbols (values: 0, 1) - this is the default
x1, x2 = symbols('x1 x2', symbol_type="binary")
# or simply:
x1, x2 = symbols('x1 x2')  # defaults to binary

# Create arrays of symbols with specific types
ising_array = symbols_list([3, 3], 's{}_{}', symbol_type="ising")
binary_array = symbols_list([3, 3], 'x{}_{}', symbol_type="binary")
```

### 2. Automatic Ising to QUBO Conversion

When you use Ising symbols in expressions, the `Compile().get_qubo()` method automatically converts them to QUBO format using the mathematical transformation `s = 2x - 1`:

```python
from tytan import symbols, Compile

# Create Ising symbols
s1, s2 = symbols('s1 s2', symbol_type="ising")

# Define an Ising constraint: s1 should equal s2
H_ising = (s1 - s2)**2

# Automatically converts to QUBO format
qubo, offset = Compile(H_ising).get_qubo()
print("QUBO matrix:", qubo[0])
print("Offset:", offset)
```

### 3. Mathematical Correctness

The conversion follows the standard Ising to QUBO transformation:
- **Ising variables**: s ∈ {-1, +1}
- **Binary variables**: x ∈ {0, 1}
- **Transformation**: s = 2x - 1, or equivalently x = (s + 1)/2

For example, the Ising expression `s1 * s2` becomes:
```
s1 * s2 = (2*x1 - 1) * (2*x2 - 1) = 4*x1*x2 - 2*x1 - 2*x2 + 1
```

### 4. Utility Functions

New utility functions for managing symbol types:

```python
from tytan import get_symbol_type, set_symbol_type, clear_symbol_registry

# Check symbol type
s = symbols('s', symbol_type="ising")
print(get_symbol_type(s))  # Output: "ising"

# Set symbol type manually
x = symbols('x')
set_symbol_type(x, "ising")

# Clear the symbol registry
clear_symbol_registry()
```

## Examples

### Example 1: Basic Ising Constraint

```python
from tytan import symbols, Compile

# Problem: Two Ising spins should have the same value
s1, s2 = symbols('s1 s2', symbol_type="ising")
H = (s1 - s2)**2

# Convert to QUBO
qubo, offset = Compile(H).get_qubo()
# This automatically handles the Ising to QUBO conversion
```

### Example 2: Mixed Variable Types

```python
from tytan import symbols, Compile

# Mix Ising and binary variables
s = symbols('s', symbol_type="ising")  # Ising spin
x = symbols('x', symbol_type="binary")  # Binary decision

# Coupling between spin and decision
H = s * x

# Automatically converts Ising part to binary for QUBO
qubo, offset = Compile(H).get_qubo()
```

### Example 3: Practical Optimization Problem

```python
from tytan import symbols, Compile

# Problem: Select exactly 2 out of 3 items using Ising formulation
s1, s2, s3 = symbols('s1 s2 s3', symbol_type="ising")

# Constraint: exactly 2 variables should be +1
H = (s1 + s2 + s3 - 2)**2

# Convert to QUBO for solving
qubo, offset = Compile(H).get_qubo()
```

## Backward Compatibility

All existing TYTAN code continues to work without any modifications:

- `symbols()` without `symbol_type` parameter defaults to "binary"
- `symbols_list()` without `symbol_type` parameter defaults to "binary"
- `symbols_define()` generates the same output for binary symbols
- All existing QUBO compilation works exactly as before

## Implementation Details

### Files Modified

1. **`tytan/symbol.py`**: Enhanced with symbol type support
   - Added global symbol type registry
   - Enhanced all symbol creation functions with `symbol_type` parameter
   - Added utility functions for type management

2. **`tytan/compile.py`**: Enhanced QUBO compilation
   - Added Ising symbol detection
   - Implemented automatic Ising to QUBO conversion
   - Maintains backward compatibility

3. **`tytan/__init__.py`**: Updated exports
   - Added new utility functions to public API

### Mathematical Foundation

The conversion uses the standard transformation between Ising and QUBO models:

**Ising Model**: E(s) = Σ hᵢsᵢ + Σ Jᵢⱼsᵢsⱼ where s ∈ {-1, +1}

**QUBO Model**: E(x) = Σ aᵢxᵢ + Σ bᵢⱼxᵢxⱼ where x ∈ {0, 1}

**Transformation**: s = 2x - 1

## Testing

Comprehensive tests have been added to verify:
- Symbol type declaration functionality
- Ising to QUBO conversion correctness
- Mixed variable type support
- Mathematical accuracy of transformations
- Backward compatibility with existing code

Run tests with:
```bash
python test_ising_enhancement.py
python demo_ising_enhancement.py
```

## Benefits

1. **Natural Problem Formulation**: Use Ising variables for problems naturally expressed with {-1, +1} values
2. **Automatic Conversion**: No manual transformation needed - the library handles it automatically
3. **Mathematical Correctness**: Verified implementation of standard Ising ↔ QUBO transformations
4. **Flexibility**: Support for mixed variable types in the same expression
5. **Backward Compatibility**: Existing code works without changes

This enhancement makes the TYTAN library more versatile for quantum optimization problems while maintaining its ease of use and reliability.
