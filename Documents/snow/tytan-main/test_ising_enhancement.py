#!/usr/bin/env python3
"""
Test script for the enhanced symbol creation functionality with Ising support.
This script tests:
1. Symbol type declaration (binary/ising)
2. QUBO matrix generation for Ising symbols
3. Mathematical correctness of Ising to QUBO conversion
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tytan'))

from tytan import symbols, symbols_list, Compile, get_symbol_type, set_symbol_type, clear_symbol_registry
import numpy as np

def test_symbol_type_declaration():
    """Test basic symbol type declaration functionality."""
    print("=== Testing Symbol Type Declaration ===")
    
    # Clear registry for clean test
    clear_symbol_registry()
    
    # Test binary symbols (default)
    x = symbols('x')
    assert get_symbol_type(x) == "binary", f"Expected binary, got {get_symbol_type(x)}"
    print("✓ Default binary symbol creation works")
    
    # Test explicit binary symbols
    y = symbols('y', symbol_type="binary")
    assert get_symbol_type(y) == "binary", f"Expected binary, got {get_symbol_type(y)}"
    print("✓ Explicit binary symbol creation works")
    
    # Test Ising symbols
    s = symbols('s', symbol_type="ising")
    assert get_symbol_type(s) == "ising", f"Expected ising, got {get_symbol_type(s)}"
    print("✓ Ising symbol creation works")
    
    # Test multiple symbols
    a, b, c = symbols('a b c', symbol_type="ising")
    assert get_symbol_type(a) == "ising", f"Expected ising, got {get_symbol_type(a)}"
    assert get_symbol_type(b) == "ising", f"Expected ising, got {get_symbol_type(b)}"
    assert get_symbol_type(c) == "ising", f"Expected ising, got {get_symbol_type(c)}"
    print("✓ Multiple Ising symbols creation works")
    
    print("Symbol type declaration tests passed!\n")

def test_symbols_list_with_types():
    """Test symbols_list with different types."""
    print("=== Testing symbols_list with Types ===")
    
    clear_symbol_registry()
    
    # Test binary symbols list
    q_bin = symbols_list([2, 2], 'q{}_{}', symbol_type="binary")
    assert get_symbol_type(q_bin[0, 0]) == "binary"
    assert get_symbol_type(q_bin[1, 1]) == "binary"
    print("✓ Binary symbols_list works")
    
    # Test Ising symbols list
    s_ising = symbols_list([2, 2], 's{}_{}', symbol_type="ising")
    assert get_symbol_type(s_ising[0, 0]) == "ising"
    assert get_symbol_type(s_ising[1, 1]) == "ising"
    print("✓ Ising symbols_list works")
    
    print("symbols_list type tests passed!\n")

def test_simple_ising_to_qubo():
    """Test simple Ising to QUBO conversion."""
    print("=== Testing Simple Ising to QUBO Conversion ===")
    
    clear_symbol_registry()
    
    # Create Ising symbols
    s1 = symbols('s1', symbol_type="ising")
    s2 = symbols('s2', symbol_type="ising")
    
    # Simple Ising model: E = -0.5 * s1 * s2
    # This should convert to QUBO using s = 2x - 1
    # s1 * s2 = (2*x1 - 1) * (2*x2 - 1) = 4*x1*x2 - 2*x1 - 2*x2 + 1
    # So -0.5 * s1 * s2 = -2*x1*x2 + x1 + x2 - 0.5
    H_ising = -0.5 * s1 * s2
    
    try:
        qubo, offset = Compile(H_ising).get_qubo()
        print(f"✓ QUBO compilation successful")
        print(f"  QUBO matrix shape: {qubo[0].shape}")
        print(f"  Offset: {offset}")
        print(f"  Index map: {qubo[1]}")
        
        # The QUBO matrix should have the converted coefficients
        print(f"  QUBO matrix:\n{qubo[0]}")
        
    except Exception as e:
        print(f"✗ QUBO compilation failed: {e}")
        return False
    
    print("Simple Ising to QUBO conversion test passed!\n")
    return True

def test_mixed_symbols():
    """Test expressions with both Ising and binary symbols."""
    print("=== Testing Mixed Symbol Types ===")
    
    clear_symbol_registry()
    
    # Create mixed symbols
    s = symbols('s', symbol_type="ising")  # Ising symbol
    x = symbols('x', symbol_type="binary")  # Binary symbol
    
    # Mixed expression: s + x
    H_mixed = s + x
    
    try:
        qubo, offset = Compile(H_mixed).get_qubo()
        print(f"✓ Mixed symbol QUBO compilation successful")
        print(f"  QUBO matrix shape: {qubo[0].shape}")
        print(f"  Offset: {offset}")
        
    except Exception as e:
        print(f"✗ Mixed symbol compilation failed: {e}")
        return False
    
    print("Mixed symbol types test passed!\n")
    return True

def test_mathematical_correctness():
    """Test mathematical correctness of the conversion."""
    print("=== Testing Mathematical Correctness ===")
    
    clear_symbol_registry()
    
    # Test the identity constraint: s1 = s2 (from D-Wave documentation)
    # This should minimize when s1 and s2 have the same value
    s1 = symbols('s1', symbol_type="ising")
    s2 = symbols('s2', symbol_type="ising")
    
    # Ising formulation: minimize (s1 - s2)^2 = s1^2 + s2^2 - 2*s1*s2
    # Since s^2 = 1 for Ising variables, this becomes: 2 - 2*s1*s2
    # The energy gap should be 4 between ground states and excited states
    H_identity = (s1 - s2)**2
    
    try:
        qubo, offset = Compile(H_identity).get_qubo()
        print(f"✓ Identity constraint QUBO compilation successful")
        print(f"  QUBO matrix:\n{qubo[0]}")
        print(f"  Offset: {offset}")
        
        # Verify the structure makes sense
        matrix = qubo[0]
        if matrix.shape[0] >= 2:
            print(f"  Diagonal elements: {np.diag(matrix)}")
            print(f"  Off-diagonal elements: {matrix[0,1] if matrix.shape[0] > 1 else 'N/A'}")
        
    except Exception as e:
        print(f"✗ Mathematical correctness test failed: {e}")
        return False
    
    print("Mathematical correctness test passed!\n")
    return True

def main():
    """Run all tests."""
    print("Starting Enhanced Symbol Creation Tests\n")
    
    tests = [
        test_symbol_type_declaration,
        test_symbols_list_with_types,
        test_simple_ising_to_qubo,
        test_mixed_symbols,
        test_mathematical_correctness
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            result = test()
            if result is not False:
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}\n")
    
    print(f"=== Test Summary ===")
    print(f"Passed: {passed}/{total}")
    print(f"Success rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed.")
        return 1

if __name__ == "__main__":
    exit(main())
