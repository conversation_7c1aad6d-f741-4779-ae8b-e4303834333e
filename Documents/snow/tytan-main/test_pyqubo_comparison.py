#!/usr/bin/env python3
"""
Test to compare our Ising to QUBO conversion with PyQUBO results.
This verifies that our implementation produces the same QUBO matrix as PyQUBO.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tytan'))

from tytan import symbols, symbols_list, Compile, clear_symbol_registry
import numpy as np
from pprint import pprint

def test_pyqubo_comparison():
    """
    Test the same problem as in PyQUBO documentation:
    numbers = [4, 2, 7, 1]
    s = Array.create('s', shape=4, vartype='SPIN')
    H = sum(n * s for s, n in zip(s, numbers))**2
    """
    print("=== PyQUBO Comparison Test ===")
    
    clear_symbol_registry()
    
    # Create the same problem as PyQUBO example
    numbers = [4, 2, 7, 1]
    
    # Create Ising symbols (equivalent to PyQUBO's SPIN variables)
    s = symbols_list([4], 's{}', symbol_type="ising")
    
    print(f"Numbers: {numbers}")
    print(f"Created Ising symbols: s0, s1, s2, s3")
    
    # Build the same expression: sum(n * s for s, n in zip(s, numbers))**2
    H = sum(n * s_i for s_i, n in zip(s, numbers))**2
    
    print(f"Expression: (4*s0 + 2*s1 + 7*s2 + 1*s3)^2")
    
    # Convert to QUBO using our implementation
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"\nOur QUBO conversion:")
    print(f"Offset: {offset}")
    print(f"Index mapping: {index_map}")
    print(f"QUBO matrix shape: {matrix.shape}")
    
    # Convert matrix to PyQUBO-style dictionary format for comparison
    our_qubo_dict = {}
    var_names = list(index_map.keys())
    
    for i in range(len(var_names)):
        for j in range(i, len(var_names)):
            if matrix[i, j] != 0:
                # Convert back to original variable names (remove _bin suffix)
                var_i = var_names[i].replace('_bin', '')
                var_j = var_names[j].replace('_bin', '')
                our_qubo_dict[(var_i, var_j)] = matrix[i, j]
    
    print(f"\nOur QUBO dictionary:")
    pprint(our_qubo_dict)
    
    # Expected PyQUBO result (from the documentation)
    expected_pyqubo = {
        ('s0', 's0'): -160.0,
        ('s0', 's1'): 64.0,
        ('s0', 's2'): 224.0,
        ('s0', 's3'): 32.0,
        ('s1', 's1'): -96.0,
        ('s1', 's2'): 112.0,
        ('s1', 's3'): 16.0,
        ('s2', 's2'): -196.0,
        ('s2', 's3'): 56.0,
        ('s3', 's3'): -52.0
    }
    
    print(f"\nExpected PyQUBO result:")
    pprint(expected_pyqubo)
    
    # Compare results
    print(f"\n=== Comparison ===")
    
    # Check if all expected keys are present
    missing_keys = []
    extra_keys = []
    value_differences = []
    
    for key in expected_pyqubo:
        if key not in our_qubo_dict:
            missing_keys.append(key)
        else:
            expected_val = expected_pyqubo[key]
            our_val = our_qubo_dict[key]
            if abs(expected_val - our_val) > 1e-10:
                value_differences.append((key, expected_val, our_val))
    
    for key in our_qubo_dict:
        if key not in expected_pyqubo:
            extra_keys.append(key)
    
    # Report results
    if not missing_keys and not extra_keys and not value_differences:
        print("✅ PERFECT MATCH! Our implementation produces identical results to PyQUBO")
        return True
    else:
        print("❌ DIFFERENCES FOUND:")
        if missing_keys:
            print(f"Missing keys: {missing_keys}")
        if extra_keys:
            print(f"Extra keys: {extra_keys}")
        if value_differences:
            print("Value differences:")
            for key, expected, actual in value_differences:
                print(f"  {key}: expected {expected}, got {actual}, diff = {abs(expected - actual)}")
        return False

def manual_verification():
    """
    Manually verify the mathematical transformation for a simple case.
    """
    print("\n=== Manual Mathematical Verification ===")
    
    # Let's manually verify for a simple case: (4*s0 + 2*s1)^2
    print("Verifying: (4*s0 + 2*s1)^2 where s0, s1 ∈ {-1, +1}")
    
    # Expand: (4*s0 + 2*s1)^2 = 16*s0^2 + 16*s0*s1 + 4*s1^2
    # Since s^2 = 1 for Ising variables: = 16 + 16*s0*s1 + 4 = 20 + 16*s0*s1
    print("Ising expansion: 16*s0^2 + 16*s0*s1 + 4*s1^2 = 16 + 16*s0*s1 + 4 = 20 + 16*s0*s1")
    
    # Convert to binary using s = 2*x - 1:
    # s0*s1 = (2*x0 - 1)*(2*x1 - 1) = 4*x0*x1 - 2*x0 - 2*x1 + 1
    # So: 20 + 16*s0*s1 = 20 + 16*(4*x0*x1 - 2*x0 - 2*x1 + 1)
    #                   = 20 + 64*x0*x1 - 32*x0 - 32*x1 + 16
    #                   = 36 + 64*x0*x1 - 32*x0 - 32*x1
    #                   = -32*x0 - 32*x1 + 64*x0*x1 + 36
    
    print("Binary conversion:")
    print("s0*s1 = (2*x0-1)*(2*x1-1) = 4*x0*x1 - 2*x0 - 2*x1 + 1")
    print("20 + 16*s0*s1 = 20 + 16*(4*x0*x1 - 2*x0 - 2*x1 + 1)")
    print("               = 20 + 64*x0*x1 - 32*x0 - 32*x1 + 16")
    print("               = 36 + 64*x0*x1 - 32*x0 - 32*x1")
    print("Expected QUBO: x0 coeff = -32, x1 coeff = -32, x0*x1 coeff = 64, offset = 36")
    
    # Test with our implementation
    clear_symbol_registry()
    s0, s1 = symbols('s0 s1', symbol_type="ising")
    H_simple = (4*s0 + 2*s1)**2
    
    qubo, offset = Compile(H_simple).get_qubo()
    matrix, index_map = qubo
    
    print(f"\nOur implementation result:")
    print(f"Matrix:\n{matrix}")
    print(f"Offset: {offset}")
    print(f"Index map: {index_map}")
    
    # Extract coefficients
    if len(matrix) >= 2:
        x0_coeff = matrix[0, 0]
        x1_coeff = matrix[1, 1] 
        x0_x1_coeff = matrix[0, 1]
        
        print(f"x0 coefficient: {x0_coeff} (expected: -32)")
        print(f"x1 coefficient: {x1_coeff} (expected: -32)")
        print(f"x0*x1 coefficient: {x0_x1_coeff} (expected: 64)")
        print(f"Offset: {offset} (expected: 36)")
        
        # Check if they match
        if (abs(x0_coeff - (-32)) < 1e-10 and 
            abs(x1_coeff - (-32)) < 1e-10 and 
            abs(x0_x1_coeff - 64) < 1e-10 and 
            abs(offset - 36) < 1e-10):
            print("✅ Manual verification PASSED!")
            return True
        else:
            print("❌ Manual verification FAILED!")
            return False
    else:
        print("❌ Matrix size unexpected")
        return False

def main():
    """Run all comparison tests."""
    print("🔍 Testing TYTAN Ising to QUBO conversion against PyQUBO")
    print("=" * 60)
    
    # Test 1: Full PyQUBO comparison
    test1_passed = test_pyqubo_comparison()
    
    # Test 2: Manual verification
    test2_passed = manual_verification()
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"PyQUBO comparison: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Manual verification: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests PASSED! Our implementation matches PyQUBO exactly.")
        return 0
    else:
        print("\n⚠️  Some tests FAILED. Implementation needs review.")
        return 1

if __name__ == "__main__":
    exit(main())
