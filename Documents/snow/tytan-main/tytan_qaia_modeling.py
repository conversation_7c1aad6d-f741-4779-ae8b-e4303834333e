#!/usr/bin/env python3
"""
Tytan QAIA equivalent of PyQUBO modeling for optimization problems.

This module demonstrates how to rewrite PyQUBO-based optimization models
using <PERSON><PERSON>'s QAIA sampler with automatic QUBO-to-Ising conversion.
"""

import numpy as np
import time
from tytan import symbols, Compile
from tytan.qaia_sampler import QAIASampler


def use_annealing_tytan(C, BR, alpha, beta, gamma, num_reads, junc_info, 
                       algorithm='asb', backend='cpu-float32', seed=42):
    """
    Tytan QAIA equivalent of the original PyQUBO use_annealing function.
    
    Args:
        C: Cost matrix for first objective term
        BR: Binary relationship matrix for second objective term  
        alpha: Weight for first objective term
        beta: Weight for second objective term
        gamma: Weight for constraint penalty term
        num_reads: Number of samples (shots in QAIA)
        junc_info: Junction information dictionary
        algorithm: QAIA algorithm ('asb', 'bsb', 'dsb')
        backend: Computation backend ('cpu-float32', 'gpu-float32', 'npu-float32')
        seed: Random seed for reproducibility
        
    Returns:
        tuple: (best_solution_dict, all_results, elapsed_time)
    """
    
    # Create binary variables using Tytan symbols
    # Format: x[i][m] -> x_i_m
    x = {}
    for (i, m) in C.keys():
        var_name = f"x_{i}_{m}"
        x[(i, m)] = symbols(var_name, symbol_type="binary")
    
    # Objective function first term: sum of C[i,m] * x[i,m]
    H1 = sum(C[i, m] * x[i, m] for (i, m) in C.keys())
    
    # Objective function second term: sum of BR[i,u,j,v] * x[i,u] * x[j,v]
    H2 = sum(BR[i, u, j, v] * x[i, u] * x[j, v] for (i, u, j, v) in BR.keys())
    
    # Constraint term: each junction should have exactly one mode selected
    # (sum of x[i,m] for all modes m of junction i - 1)^2
    H3_terms = []
    for i in junc_info.keys():
        # Sum all modes for junction i
        mode_sum = sum(x[i, m] for m in range(junc_info[i]['num_modes']) 
                      if (i, m) in x)
        # Constraint: sum should equal 1
        constraint_term = (mode_sum - 1) ** 2
        H3_terms.append(constraint_term)
    
    H3 = sum(H3_terms)
    
    # Combined objective function (minimization)
    # Note: In PyQUBO, negative signs were used for maximization terms
    # Here we directly construct the minimization objective
    H = -alpha * H1 - beta * H2 + gamma * H3
    
    # Compile to QUBO using Tytan
    qubo, offset = Compile(H).get_qubo()
    
    # Initialize QAIA sampler
    sampler = QAIASampler(
        algorithm=algorithm,
        seed=seed,
        backend=backend
    )
    
    # Run optimization
    start_time = time.time()
    
    # Use conservative parameters to avoid overflow
    result = sampler.run(
        qubo, 
        shots=num_reads,
        n_iter=1000,  # Good default for convergence
        dt=0.1,       # Conservative time step
        xi=0.1        # Conservative coupling strength
    )
    
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # Extract best solution
    best_solution_raw = result[0][0]  # Dictionary with variable names as keys
    
    # Convert back to original format: x_i_m -> x[i][m]
    best_solution = {}
    for var_name, value in best_solution_raw.items():
        if var_name.startswith('x_'):
            # Parse x_i_m format
            parts = var_name.split('_')
            if len(parts) == 3:
                i, m = int(parts[1]), int(parts[2])
                if i not in best_solution:
                    best_solution[i] = {}
                best_solution[i][m] = value
    
    # Apply post-processing (constraint enforcement)
    best_solution = post_processing_tytan(best_solution, junc_info)
    
    return best_solution, result, elapsed_time


def post_processing_tytan(solution_dict, junc_info):
    """
    Post-processing to enforce constraints (each junction has exactly one mode).
    
    Args:
        solution_dict: Dictionary with solution values
        junc_info: Junction information dictionary
        
    Returns:
        dict: Processed solution dictionary
    """
    processed_solution = {}
    
    for i in junc_info.keys():
        if i in solution_dict:
            modes = solution_dict[i]
            
            # Find the mode with highest value (or first one if tie)
            best_mode = max(modes.keys(), key=lambda m: modes.get(m, 0))
            
            # Set only the best mode to 1, others to 0
            processed_solution[i] = {}
            for m in range(junc_info[i]['num_modes']):
                processed_solution[i][m] = 1 if m == best_mode else 0
        else:
            # If junction not in solution, set first mode to 1
            processed_solution[i] = {}
            for m in range(junc_info[i]['num_modes']):
                processed_solution[i][m] = 1 if m == 0 else 0
    
    return processed_solution


def create_sample_input():
    """
    Create sample input data for testing the optimization function.
    
    Returns:
        tuple: (C, BR, alpha, beta, gamma, num_reads, junc_info)
    """
    
    # Sample junction information
    junc_info = {
        0: {'num_modes': 3},  # Junction 0 has 3 modes
        1: {'num_modes': 2},  # Junction 1 has 2 modes  
        2: {'num_modes': 4},  # Junction 2 has 4 modes
    }
    
    # Sample cost matrix C[i,m] - cost of selecting mode m for junction i
    C = {}
    np.random.seed(42)  # For reproducible sample data
    
    for i in junc_info.keys():
        for m in range(junc_info[i]['num_modes']):
            C[(i, m)] = np.random.uniform(1.0, 10.0)  # Random costs
    
    # Sample binary relationship matrix BR[i,u,j,v] 
    # - interaction cost between mode u of junction i and mode v of junction j
    BR = {}
    
    for i in junc_info.keys():
        for j in junc_info.keys():
            if i != j:  # No self-interactions
                for u in range(junc_info[i]['num_modes']):
                    for v in range(junc_info[j]['num_modes']):
                        # Random interaction costs (can be positive or negative)
                        BR[(i, u, j, v)] = np.random.uniform(-5.0, 5.0)
    
    # Sample parameters
    alpha = 1.0   # Weight for individual costs
    beta = 0.5    # Weight for interaction costs  
    gamma = 10.0  # Weight for constraint penalty (should be large)
    num_reads = 50  # Number of samples
    
    return C, BR, alpha, beta, gamma, num_reads, junc_info


def demonstrate_tytan_qaia():
    """
    Demonstration of the Tytan QAIA modeling approach.
    """
    print("=== Tytan QAIA Modeling Demonstration ===\n")
    
    # Create sample input
    C, BR, alpha, beta, gamma, num_reads, junc_info = create_sample_input()
    
    print("Sample Problem Setup:")
    print(f"- Number of junctions: {len(junc_info)}")
    print(f"- Junction modes: {[junc_info[i]['num_modes'] for i in junc_info.keys()]}")
    print(f"- Total variables: {sum(junc_info[i]['num_modes'] for i in junc_info.keys())}")
    print(f"- Parameters: alpha={alpha}, beta={beta}, gamma={gamma}")
    print(f"- Number of samples: {num_reads}\n")
    
    # Test different QAIA algorithms
    algorithms = ['asb', 'bsb', 'dsb']
    results = {}
    
    for algorithm in algorithms:
        print(f"Testing {algorithm.upper()} algorithm...")
        
        try:
            solution, all_results, elapsed_time = use_annealing_tytan(
                C, BR, alpha, beta, gamma, num_reads, junc_info,
                algorithm=algorithm, backend='cpu-float32', seed=42
            )
            
            results[algorithm] = {
                'solution': solution,
                'energy': all_results[0][1],
                'time': elapsed_time,
                'success': True
            }
            
            print(f"  ✓ Success! Energy: {all_results[0][1]:.4f}, Time: {elapsed_time:.3f}s")
            print(f"  Solution: {solution}")
            
        except Exception as e:
            results[algorithm] = {
                'error': str(e),
                'success': False
            }
            print(f"  ✗ Failed: {e}")
        
        print()
    
    # Summary
    successful_results = {k: v for k, v in results.items() if v.get('success', False)}
    
    if successful_results:
        best_algorithm = min(successful_results.keys(), 
                           key=lambda k: successful_results[k]['energy'])
        
        print("=== Results Summary ===")
        print(f"Best algorithm: {best_algorithm.upper()}")
        print(f"Best energy: {successful_results[best_algorithm]['energy']:.4f}")
        print(f"Best solution: {successful_results[best_algorithm]['solution']}")
    else:
        print("No algorithms succeeded. Try reducing dt and xi parameters.")


if __name__ == "__main__":
    demonstrate_tytan_qaia()
