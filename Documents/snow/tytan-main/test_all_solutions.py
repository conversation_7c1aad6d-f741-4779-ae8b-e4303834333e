#!/usr/bin/env python3
"""
Test all possible solutions to see what the Ising energies should be.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler
import numpy as np
import itertools


def main():
    print("Testing all possible solutions for H = (x + y + z - 2)^2")
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print("QUBO matrix:")
    print(matrix)
    print("Index map:", index_map)
    print("Offset:", offset)
    
    # Get Ising conversion
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print("\nIssing parameters:")
    print("J matrix:")
    print(J)
    print("h vector:", h)
    print("Ising offset:", ising_offset)
    
    print("\nTesting all 8 possible solutions:")
    print("Binary -> Ising -> QUBO Energy | Ising Energy | Manual Energy")
    print("-" * 70)
    
    best_qubo_energy = float('inf')
    best_ising_energy = float('inf')
    
    for binary_sol in itertools.product([0, 1], repeat=3):
        binary_sol = np.array(binary_sol)
        ising_sol = 2 * binary_sol - 1
        
        # QUBO energy
        qubo_energy = binary_sol.T @ matrix @ binary_sol
        
        # Ising energy
        ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + ising_offset
        
        # Manual calculation
        manual_energy = (sum(binary_sol) - 2)**2
        
        # Track best energies
        if qubo_energy < best_qubo_energy:
            best_qubo_energy = qubo_energy
        if ising_energy < best_ising_energy:
            best_ising_energy = ising_energy
        
        match = abs(qubo_energy - ising_energy) < 1e-10
        
        print(f"{list(binary_sol)} -> {list(ising_sol)} -> "
              f"QUBO: {qubo_energy:5.1f} | Ising: {ising_energy:6.2f} | "
              f"Manual: {manual_energy:5.1f} | Match: {'✓' if match else '✗'}")
    
    print("-" * 70)
    print(f"Best QUBO energy: {best_qubo_energy}")
    print(f"Best Ising energy: {best_ising_energy}")
    
    if abs(best_qubo_energy - best_ising_energy) < 1e-10:
        print("✅ Conversion preserves optimal solutions")
    else:
        print("❌ Conversion does NOT preserve optimal solutions")
        print("This explains why QAIASampler finds wrong solutions!")


if __name__ == "__main__":
    main()
