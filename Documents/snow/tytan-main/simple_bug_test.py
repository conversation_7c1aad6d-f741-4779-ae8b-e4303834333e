#!/usr/bin/env python3
"""
Simple test for the QAIASampler bug fix.
"""

from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def main():
    print("Testing QAIASampler bug fix for H = (x + y + z - 2)^2")

    clear_symbol_registry()

    # Create the problematic case
    x = symbols('x')
    y = symbols('y')
    z = symbols('z')
    H = (x + y + z - 2)**2

    print("Problem: Find x, y, z such that exactly 2 are equal to 1")
    print("Expected solutions: {x:1,y:1,z:0}, {x:1,y:0,z:1}, {x:0,y:1,z:1}")

    # Compile to QUBO
    qubo, offset = Compile(H).get_qubo()

    # Test QAIASampler
    try:
        sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=100, n_iter=2000, dt=0.01, xi=0.01)

        print(f"\nQAIASampler found {len(result)} unique solutions:")

        correct_count = 0
        for i, (solution, energy, count) in enumerate(result[:5]):
            total = sum(solution.values())
            is_correct = (total == 2)

            print(f"Solution {i+1}: {solution}")
            print(f"  Energy: {energy}, Count: {count}, Sum: {total}")
            print(f"  Correct: {'✓' if is_correct else '✗'}")

            if is_correct:
                correct_count += 1

        if correct_count > 0:
            print(f"\n✅ BUG FIXED! Found {correct_count} correct solutions")
        else:
            print(f"\n❌ Bug still exists - no correct solutions found")

    except Exception as e:
        print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    main()
