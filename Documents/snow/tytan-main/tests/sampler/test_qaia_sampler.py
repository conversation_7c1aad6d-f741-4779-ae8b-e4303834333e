import pytest
import numpy as np
from tytan import symbols, Compile
from tytan.qaia_sampler import QAIASampler


def test_qaia_sampler_initialization():
    """Test QAIASampler initialization with different parameters."""
    # Test default initialization
    sampler = QAIASampler()
    assert sampler.algorithm == 'asb'
    assert sampler.seed is None
    assert sampler.backend == 'cpu-float32'

    # Test custom initialization
    sampler = QAIASampler(algorithm='bsb', seed=42, backend='cpu-float32')
    assert sampler.algorithm == 'bsb'
    assert sampler.seed == 42
    assert sampler.backend == 'cpu-float32'

    # Test invalid algorithm
    with pytest.raises(ValueError):
        QAIASampler(algorithm='invalid')


def test_qaia_sampler_simple_qubo():
    """Test QAIASampler on a simple QUBO problem."""
    # Create a simple QUBO problem: (x + y - 1)^2
    x, y = symbols("x y")
    expr = (x + y - 1)**2
    qubo, offset = Compile(expr).get_qubo()

    # Test with ASB algorithm
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=10, n_iter=100)

    # Verify result format
    assert isinstance(result, list)
    assert len(result) > 0
    assert isinstance(result[0], list)
    assert len(result[0]) == 3  # [solution_dict, energy, count]
    assert isinstance(result[0][0], dict)
    assert 'x' in result[0][0]
    assert 'y' in result[0][0]
    assert isinstance(result[0][1], (int, float, np.number))
    assert isinstance(result[0][2], (int, np.integer))

    # Check that solutions are binary (0 or 1)
    for sol_data in result:
        solution = sol_data[0]
        for var, val in solution.items():
            assert val in [0, 1], f"Variable {var} has non-binary value {val}"


def test_qaia_sampler_all_algorithms():
    """Test all QAIA algorithms (ASB, BSB, DSB)."""
    # Create a simple QUBO problem
    x, y, z = symbols("x y z")
    expr = (x + y + z - 2)**2
    qubo, offset = Compile(expr).get_qubo()

    algorithms = ['asb', 'bsb', 'dsb']

    for algorithm in algorithms:
        sampler = QAIASampler(algorithm=algorithm, seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=5, n_iter=50)

        # Verify result format
        assert isinstance(result, list)
        assert len(result) > 0

        # Check that all variables are present
        solution = result[0][0]
        assert 'x' in solution
        assert 'y' in solution
        assert 'z' in solution

        # Check binary values
        for var, val in solution.items():
            assert val in [0, 1]


def test_qaia_sampler_qubo_to_ising_conversion():
    """Test the QUBO to Ising conversion functionality."""
    # Create a simple 2-variable QUBO with a constant term to avoid compilation issues
    x, y = symbols("x y")
    expr = x * y + x + y + 1  # Include constant term
    qubo, offset = Compile(expr).get_qubo()

    sampler = QAIASampler(algorithm='asb', backend='cpu-float32')

    # Test the internal conversion method
    qubo_matrix, index_map = qubo
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(qubo_matrix, index_map)

    # Verify shapes
    assert J.shape == qubo_matrix.shape
    assert h.shape == (qubo_matrix.shape[0],)
    assert ising_index_map == index_map
    assert isinstance(ising_offset, (int, float))


def test_qaia_sampler_ising_to_binary_conversion():
    """Test the Ising to binary solution conversion."""
    sampler = QAIASampler()

    # Test conversion with known values
    ising_solution = np.array([[-1, 1, -1], [1, -1, 1]])  # 2 samples, 3 variables each
    binary_solution = sampler._ising_to_binary_solution(ising_solution)

    expected = np.array([[0, 1, 0], [1, 0, 1]])
    np.testing.assert_array_equal(binary_solution, expected)


def test_qaia_sampler_reproducibility():
    """Test that results are reproducible with the same seed."""
    x, y = symbols("x y")
    expr = (x + y - 1)**2
    qubo, offset = Compile(expr).get_qubo()

    # Run twice with the same seed
    sampler1 = QAIASampler(algorithm='asb', seed=123, backend='cpu-float32')
    result1 = sampler1.run(qubo, shots=5, n_iter=50)

    sampler2 = QAIASampler(algorithm='asb', seed=123, backend='cpu-float32')
    result2 = sampler2.run(qubo, shots=5, n_iter=50)

    # Results should be identical
    assert len(result1) == len(result2)
    # Note: Due to the nature of the algorithm, exact reproducibility might vary
    # but the best energy should be similar
    assert abs(result1[0][1] - result2[0][1]) < 1e-6


def test_qaia_sampler_error_handling():
    """Test error handling for invalid inputs."""
    sampler = QAIASampler()

    # Test with invalid QUBO matrix (not square)
    invalid_matrix = np.array([[1, 2, 3], [4, 5, 6]])
    index_map = {'x': 0, 'y': 1}

    with pytest.raises(ValueError):
        sampler.run((invalid_matrix, index_map))

    # Test with mismatched index map
    valid_matrix = np.array([[1, 2], [2, 3]])
    mismatched_index_map = {'x': 0, 'y': 1, 'z': 2}  # Too many variables

    with pytest.raises(ValueError):
        sampler.run((valid_matrix, mismatched_index_map))


def test_qaia_sampler_larger_problem():
    """Test QAIASampler on a larger QUBO problem."""
    # Create a simpler 4-variable problem to avoid overflow
    x1, x2, x3, x4 = symbols("x1 x2 x3 x4")
    expr = (x1 + x2 + x3 + x4 - 2)**2  # Simpler constraint without additional terms
    qubo, offset = Compile(expr).get_qubo()

    # Use smaller parameters to avoid overflow
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=10, n_iter=100, dt=0.1, xi=0.1)

    # Verify result format
    assert isinstance(result, list)
    assert len(result) > 0

    # Check that all variables are present
    solution = result[0][0]
    assert 'x1' in solution
    assert 'x2' in solution
    assert 'x3' in solution
    assert 'x4' in solution

    # Check binary values
    for var, val in solution.items():
        assert val in [0, 1]

    # Verify energy calculation makes sense (should be a finite number)
    assert np.isfinite(result[0][1])


def test_qaia_sampler_with_ising_symbols():
    """Test QAIASampler with Ising symbols."""
    from tytan import clear_symbol_registry

    clear_symbol_registry()

    # Create Ising symbols as requested: symbols('s1 s2', symbol_type="ising")
    s1, s2 = symbols('s1 s2', symbol_type="ising")

    # Ising constraint: s1 should equal s2
    expr = (s1 - s2)**2
    qubo, offset = Compile(expr).get_qubo()

    # Test with ASB algorithm
    sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=10, n_iter=50, dt=0.1, xi=0.1)

    # Verify result format
    assert isinstance(result, list)
    assert len(result) > 0
    assert isinstance(result[0], list)
    assert len(result[0]) == 3
    assert isinstance(result[0][0], dict)

    # Check that Ising symbols were converted to binary format with _bin suffix
    solution = result[0][0]
    assert 's1_bin' in solution
    assert 's2_bin' in solution

    # Check binary values
    for var, val in solution.items():
        assert val in [0, 1], f"Variable {var} has non-binary value {val}"

    # Convert back to Ising and check constraint
    s1_ising = 2 * solution['s1_bin'] - 1
    s2_ising = 2 * solution['s2_bin'] - 1

    # For the constraint (s1 - s2)^2, minimum energy occurs when s1 = s2
    # So we expect s1_ising == s2_ising for the best solution
    assert s1_ising == s2_ising, f"Ising constraint not satisfied: s1={s1_ising}, s2={s2_ising}"


def test_qaia_sampler_mixed_symbol_types():
    """Test QAIASampler with mixed binary and Ising symbols."""
    from tytan import clear_symbol_registry

    clear_symbol_registry()

    # Create mixed symbols
    x = symbols('x', symbol_type="binary")
    s = symbols('s', symbol_type="ising")

    # Mixed expression
    expr = x * s + x + s + 1  # Add constant to avoid compilation issues
    qubo, offset = Compile(expr).get_qubo()

    # Test with BSB algorithm
    sampler = QAIASampler(algorithm='bsb', seed=42, backend='cpu-float32')
    result = sampler.run(qubo, shots=5, n_iter=30, dt=0.1, xi=0.1)

    # Verify result format
    assert isinstance(result, list)
    assert len(result) > 0

    solution = result[0][0]

    # Check that we have both binary and converted Ising variables
    assert 'x' in solution  # Binary variable keeps original name
    assert 's_bin' in solution  # Ising variable gets _bin suffix

    # Check binary values
    for var, val in solution.items():
        assert val in [0, 1], f"Variable {var} has non-binary value {val}"


if __name__ == "__main__":
    # Run basic tests
    test_qaia_sampler_initialization()
    test_qaia_sampler_simple_qubo()
    test_qaia_sampler_all_algorithms()
    test_qaia_sampler_qubo_to_ising_conversion()
    test_qaia_sampler_ising_to_binary_conversion()
    test_qaia_sampler_reproducibility()
    test_qaia_sampler_error_handling()
    test_qaia_sampler_larger_problem()
    test_qaia_sampler_with_ising_symbols()
    test_qaia_sampler_mixed_symbol_types()
    print("All tests passed!")
