# PyQUBO to Tytan QAIA Conversion Guide

This document provides a comprehensive comparison between PyQUBO modeling and Tytan QAIA modeling, showing how to convert optimization problems from one framework to the other.

## Key Differences Summary

| Aspect | PyQUBO | Tytan QAIA |
|--------|---------|------------|
| **Variable Creation** | `Binary(f"x[{i}][{m}]")` | `symbols(f"x_{i}_{m}", symbol_type="binary")` |
| **Placeholders** | `Placeholder("alpha")` | Direct coefficient usage |
| **Constraints** | `Constraint(expr, "name")` | Direct penalty terms |
| **Model Compilation** | `model.compile()` + `to_qubo()` | `Compile(expr).get_qubo()` |
| **Sampling** | Generic sampler interface | `QAIASampler` with algorithm selection |
| **Symbol Types** | Binary only | Binary and Ising support |

## Detailed Conversion Examples

### 1. Variable Creation

**PyQUBO:**
```python
from pyqubo import Binary

# Create binary variables
x = {(i, m): Binary(f"x[{i}][{m}]") for i, m in C.keys()}
```

**Tytan QAIA:**
```python
from tytan import symbols

# Create binary variables
x = {}
for (i, m) in C.keys():
    var_name = f"x_{i}_{m}"
    x[(i, m)] = symbols(var_name, symbol_type="binary")
```

### 2. Objective Function Construction

**PyQUBO:**
```python
from pyqubo import Placeholder, Constraint

# Objective terms
H1 = np.sum([C[i, m] * x[i, m] for i, m in C.keys()])
H2 = np.sum([BR[i, u, j, v] * x[i, u] * x[j, v] for i, u, j, v in BR.keys()])
H3 = np.sum([(np.sum([x[i, m] for m in range(junc_info[i]['num_modes'])]) - 1) ** 2 
            for i in junc_info.keys()])

# Combined objective with placeholders
H = (
    -Placeholder("alpha") * H1
    - Placeholder("beta") * H2
    + Placeholder("gamma") * Constraint(H3, "H3")
)
```

**Tytan QAIA:**
```python
# Objective terms (same mathematical structure)
H1 = sum(C[i, m] * x[i, m] for (i, m) in C.keys())
H2 = sum(BR[i, u, j, v] * x[i, u] * x[j, v] for (i, u, j, v) in BR.keys())

# Constraint terms
H3_terms = []
for i in junc_info.keys():
    mode_sum = sum(x[i, m] for m in range(junc_info[i]['num_modes']) 
                  if (i, m) in x)
    constraint_term = (mode_sum - 1) ** 2
    H3_terms.append(constraint_term)
H3 = sum(H3_terms)

# Combined objective with direct coefficients
H = -alpha * H1 - beta * H2 + gamma * H3
```

### 3. Model Compilation

**PyQUBO:**
```python
# Compile model
model = H.compile()
feed_dict = {"alpha": alpha, "beta": beta, "gamma": gamma}
qubo, offset = model.to_qubo(feed_dict=feed_dict)
```

**Tytan QAIA:**
```python
from tytan import Compile

# Direct compilation
qubo, offset = Compile(H).get_qubo()
```

### 4. Sampling/Optimization

**PyQUBO:**
```python
# Generic sampler interface
sampleset = sampler.sample_qubo(qubo, num_reads=num_reads)
lowest_sample = sampleset.first.sample
```

**Tytan QAIA:**
```python
from tytan.qaia_sampler import QAIASampler

# Initialize QAIA sampler
sampler = QAIASampler(
    algorithm='asb',  # Choose: 'asb', 'bsb', 'dsb'
    seed=42,
    backend='cpu-float32'
)

# Run optimization
result = sampler.run(
    qubo, 
    shots=num_reads,
    n_iter=1000,
    dt=0.1,
    xi=0.1
)

# Extract best solution
best_solution_raw = result[0][0]
```

## Complete Function Conversion

### Original PyQUBO Function

```python
def use_annealing(C, BR, alpha, beta, gamma, num_reads, sampler, junc_info):
    # Binary variables
    x = {(i, m): Binary(f"x[{i}][{m}]") for i, m in C.keys()}

    # Objective function terms
    H1 = np.sum([C[i, m] * x[i, m] for i, m in C.keys()])
    H2 = np.sum([BR[i, u, j, v] * x[i, u] * x[j, v] for i, u, j, v in BR.keys()])
    H3 = np.sum([(np.sum([x[i, m] for m in range(junc_info[i]['num_modes'])]) - 1) ** 2 
                for i in junc_info.keys()])

    # Combined objective
    H = (
        -Placeholder("alpha") * H1
        - Placeholder("beta") * H2
        + Placeholder("gamma") * Constraint(H3, "H3")
    )

    # Compile and solve
    model = H.compile()
    feed_dict = {"alpha": alpha, "beta": beta, "gamma": gamma}
    qubo, offset = model.to_qubo(feed_dict=feed_dict)

    start_time = time.time()
    sampleset = sampler.sample_qubo(qubo, num_reads=num_reads)
    end_time = time.time()
    elapsed_time = end_time - start_time

    lowest_sample = sampleset.first.sample
    lowest_dict = convert_sol(lowest_sample)
    lowest_dict = post_processing(lowest_dict, junc_info)

    return lowest_dict, sampleset, elapsed_time
```

### Converted Tytan QAIA Function

```python
def use_annealing_tytan(C, BR, alpha, beta, gamma, num_reads, junc_info, 
                       algorithm='asb', backend='cpu-float32', seed=42):
    # Binary variables
    x = {}
    for (i, m) in C.keys():
        var_name = f"x_{i}_{m}"
        x[(i, m)] = symbols(var_name, symbol_type="binary")
    
    # Objective function terms
    H1 = sum(C[i, m] * x[i, m] for (i, m) in C.keys())
    H2 = sum(BR[i, u, j, v] * x[i, u] * x[j, v] for (i, u, j, v) in BR.keys())
    
    # Constraint terms
    H3_terms = []
    for i in junc_info.keys():
        mode_sum = sum(x[i, m] for m in range(junc_info[i]['num_modes']) 
                      if (i, m) in x)
        constraint_term = (mode_sum - 1) ** 2
        H3_terms.append(constraint_term)
    H3 = sum(H3_terms)
    
    # Combined objective
    H = -alpha * H1 - beta * H2 + gamma * H3
    
    # Compile and solve
    qubo, offset = Compile(H).get_qubo()
    
    sampler = QAIASampler(algorithm=algorithm, seed=seed, backend=backend)
    
    start_time = time.time()
    result = sampler.run(qubo, shots=num_reads, n_iter=1000, dt=0.1, xi=0.1)
    end_time = time.time()
    elapsed_time = end_time - start_time
    
    # Process solution
    best_solution_raw = result[0][0]
    best_solution = convert_solution_format(best_solution_raw)
    best_solution = post_processing_tytan(best_solution, junc_info)
    
    return best_solution, result, elapsed_time
```

## Advantages of Tytan QAIA

1. **Simplified Syntax**: No need for Placeholder and Constraint wrappers
2. **Direct Coefficient Usage**: Parameters are used directly in expressions
3. **Multiple Algorithms**: Easy access to ASB, BSB, and DSB algorithms
4. **Symbol Type Support**: Both binary and Ising symbols supported
5. **Automatic Conversion**: QUBO-to-Ising conversion handled automatically
6. **Consistent Interface**: Unified sampler interface across algorithms

## Migration Checklist

When converting from PyQUBO to Tytan QAIA:

- [ ] Replace `Binary()` with `symbols(..., symbol_type="binary")`
- [ ] Remove `Placeholder()` and use direct coefficients
- [ ] Remove `Constraint()` wrapper and use direct penalty terms
- [ ] Replace `model.compile()` + `to_qubo()` with `Compile().get_qubo()`
- [ ] Replace generic sampler with `QAIASampler`
- [ ] Update solution extraction and processing logic
- [ ] Test with different QAIA algorithms (ASB, BSB, DSB)
- [ ] Adjust parameters (dt, xi) if needed for stability

## Performance Considerations

- **ASB**: Generally provides best solution quality
- **BSB/DSB**: Faster execution, may sacrifice some solution quality
- **Parameters**: Use `dt=0.1, xi=0.1` for stability, increase for speed
- **Backend**: Use 'gpu-float32' for larger problems if CUDA available

## Testing Your Conversion

Use the provided test script to verify your conversion:

```bash
python test_tytan_qaia_modeling.py
```

This will test:
- Basic functionality
- Constraint enforcement
- Sample input generation
- Algorithm comparison

The test suite ensures your converted model works correctly with all QAIA algorithms.
