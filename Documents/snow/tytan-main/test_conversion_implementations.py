#!/usr/bin/env python3
"""
Test different QUBO to Ising conversion implementations to find the correct one.
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

from tytan import symbols, Compile, clear_symbol_registry
import numpy as np
import itertools


def implementation_1(mat: np.ndarray):
    """Inplace-convert numpy matrix from qubo to ising."""
    mat = mat.copy()  # Don't modify original
    mat /= 4
    for i in range(mat.shape[0]):
        mat[i, i] += np.sum(mat[i, :])
    return mat


def implementation_2(Q):
    """Convert QUBO to Ising using D-Wave style format."""
    h = {}
    J = {}
    linear_offset = 0.0
    quadratic_offset = 0.0
    
    # Convert matrix to dict format first
    Q_dict = {}
    n = Q.shape[0]
    for i in range(n):
        for j in range(n):
            if Q[i, j] != 0:
                Q_dict[(i, j)] = Q[i, j]

    for (u, v), bias in Q_dict.items():
        if u == v:
            if u in h:
                h[u] += .5 * bias
            else:
                h[u] = .5 * bias
            linear_offset += bias

        else:
            if bias != 0.0:
                J[(u, v)] = .25 * bias

            if u in h:
                h[u] += .25 * bias
            else:
                h[u] = .25 * bias

            if v in h:
                h[v] += .25 * bias
            else:
                h[v] = .25 * bias

            quadratic_offset += bias

    offset = .5 * linear_offset + .25 * quadratic_offset
    
    # Convert back to matrix format
    h_vec = np.zeros(n)
    J_mat = np.zeros((n, n))
    
    for i in range(n):
        if i in h:
            h_vec[i] = h[i]
    
    for (u, v), bias in J.items():
        J_mat[u, v] = bias
    
    return J_mat, h_vec, offset


def implementation_3(Q):
    """Korean implementation."""
    n = Q.shape[0]
    h = np.zeros(n)
    J = np.zeros((n, n))
    
    # i<j에 대해 quadratic interaction term 계산
    for i in range(n):
        for j in range(n):
            if i != j:
                h[i] -= Q[i, j] / 8.0
                h[j] -= Q[i, j] / 8
                J[i, j] = Q[i, j] / 8.0

    Q_res = J
    np.fill_diagonal(Q_res, h)
    return Q_res


def implementation_4_my_current(Q):
    """My current implementation in QAIASampler."""
    n = Q.shape[0]
    J = np.zeros((n, n))
    h = np.zeros(n)
    offset = 0.0

    # J_ij = Q_ij / 4 for i < j (upper triangular)
    for i in range(n):
        for j in range(i+1, n):
            if Q[i, j] != 0:
                J[i, j] = Q[i, j] / 4
                J[j, i] = Q[i, j] / 4  # Make symmetric

    # h_i = Q_ii / 2 + sum_{j≠i} Q_ij / 4 (considering upper triangular structure)
    # First set diagonal contributions
    for i in range(n):
        h[i] = Q[i, i] / 2
    
    # Then add off-diagonal contributions (avoid double counting)
    for i in range(n):
        for j in range(i+1, n):
            if Q[i, j] != 0:  # Upper triangular
                h[i] += Q[i, j] / 4  # Contribution to h[i]
                h[j] += Q[i, j] / 4  # Contribution to h[j]

    # constant = sum_i Q_ii / 4 + sum_{i<j} Q_ij / 4
    for i in range(n):
        offset += Q[i, i] / 4  # Diagonal terms
    for i in range(n):
        for j in range(i+1, n):
            if Q[i, j] != 0:
                offset += Q[i, j] / 4  # Upper triangular off-diagonal terms
    
    return J, h, offset


def test_implementation(impl_name, impl_func, Q):
    """Test a single implementation."""
    print(f"\n=== Testing {impl_name} ===")
    
    try:
        if impl_name == "Implementation 1":
            # This returns a single matrix
            result_mat = impl_func(Q)
            print("Result matrix:")
            print(result_mat)
            # Extract J and h from the matrix
            J = result_mat.copy()
            np.fill_diagonal(J, 0)
            h = np.diag(result_mat)
            offset = 0.0
        elif impl_name == "Implementation 2":
            J, h, offset = impl_func(Q)
        elif impl_name == "Implementation 3":
            # This returns a single matrix
            result_mat = impl_func(Q)
            print("Result matrix:")
            print(result_mat)
            # Extract J and h from the matrix
            J = result_mat.copy()
            np.fill_diagonal(J, 0)
            h = np.diag(result_mat)
            offset = 0.0
        else:
            J, h, offset = impl_func(Q)
        
        print(f"J matrix:\n{J}")
        print(f"h vector: {h}")
        print(f"offset: {offset}")
        
        # Test all solutions
        print("\nTesting all solutions:")
        print("Binary -> QUBO Energy | Ising Energy | Match")
        print("-" * 50)
        
        all_match = True
        for binary_sol in itertools.product([0, 1], repeat=3):
            binary_sol = np.array(binary_sol)
            ising_sol = 2 * binary_sol - 1
            
            # QUBO energy
            qubo_energy = binary_sol.T @ Q @ binary_sol
            
            # Ising energy
            ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + offset
            
            match = abs(qubo_energy - ising_energy) < 1e-10
            if not match:
                all_match = False
            
            print(f"{list(binary_sol)} -> QUBO: {qubo_energy:5.1f} | Ising: {ising_energy:6.2f} | {'✓' if match else '✗'}")
        
        if all_match:
            print(f"✅ {impl_name} is CORRECT!")
            return True
        else:
            print(f"❌ {impl_name} is incorrect")
            return False
            
    except Exception as e:
        print(f"❌ {impl_name} failed with error: {e}")
        return False


def main():
    print("Testing different QUBO to Ising conversion implementations")
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    H = (x + y + z - 2)**2
    
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print("QUBO matrix:")
    print(matrix)
    print("Index map:", index_map)
    print("Original offset:", offset)
    
    implementations = [
        ("Implementation 1", implementation_1),
        ("Implementation 2", implementation_2),
        ("Implementation 3", implementation_3),
        ("Implementation 4 (My Current)", implementation_4_my_current),
    ]
    
    correct_implementations = []
    
    for name, func in implementations:
        if test_implementation(name, func, matrix):
            correct_implementations.append(name)
    
    print(f"\n{'='*60}")
    print("SUMMARY:")
    if correct_implementations:
        print(f"✅ Correct implementations: {', '.join(correct_implementations)}")
    else:
        print("❌ No correct implementations found!")


if __name__ == "__main__":
    main()
