#!/usr/bin/env python3
"""
Test QAIASampler with both QUBO (binary) and Ising symbols.

This script demonstrates that QAIASampler works correctly with both:
1. Binary symbols (default): x ∈ {0, 1}
2. Ising symbols: s ∈ {-1, +1}
"""

import numpy as np
from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def test_binary_symbols():
    """Test QAIASampler with traditional binary symbols."""
    print("=== Testing Binary Symbols (QUBO) ===")
    
    clear_symbol_registry()
    
    # Create binary symbols (default behavior)
    x1, x2 = symbols('x1 x2')  # symbol_type="binary" is default
    print(f"Created binary symbols: x1, x2")
    
    # Simple constraint: exactly one should be 1
    expr = (x1 + x2 - 1)**2
    print(f"Expression: (x1 + x2 - 1)^2")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"QUBO matrix:\n{matrix}")
    print(f"Variables: {list(index_map.keys())}")
    print(f"Offset: {offset}")
    
    # Test with different QAIA algorithms
    algorithms = ['asb', 'bsb', 'dsb']
    
    for algorithm in algorithms:
        print(f"\n--- Testing {algorithm.upper()} with binary symbols ---")
        try:
            sampler = QAIASampler(algorithm=algorithm, seed=42, backend='cpu-float32')
            result = sampler.run(qubo, shots=10, n_iter=50, dt=0.1, xi=0.1)
            
            best_solution = result[0][0]
            best_energy = result[0][1]
            
            print(f"Best solution: {best_solution}")
            print(f"Best energy: {best_energy}")
            
            # Verify constraint: x1 + x2 should equal 1
            constraint_value = best_solution['x1'] + best_solution['x2']
            constraint_satisfied = (constraint_value == 1)
            print(f"Constraint (x1 + x2 = 1): {constraint_value} {'✓' if constraint_satisfied else '✗'}")
            
            # Verify binary values
            for var, val in best_solution.items():
                if val not in [0, 1]:
                    print(f"❌ Non-binary value found: {var} = {val}")
                    return False
            
            print(f"✅ {algorithm.upper()} with binary symbols: SUCCESS")
            
        except Exception as e:
            print(f"❌ {algorithm.upper()} with binary symbols: FAILED - {e}")
            return False
    
    return True


def test_ising_symbols():
    """Test QAIASampler with Ising symbols."""
    print("\n\n=== Testing Ising Symbols ===")
    
    clear_symbol_registry()
    
    # Create Ising symbols
    s1, s2 = symbols('s1 s2', symbol_type="ising")
    print(f"Created Ising symbols: s1, s2")
    
    # Ising constraint: s1 should equal s2 (both +1 or both -1)
    expr = (s1 - s2)**2
    print(f"Expression: (s1 - s2)^2")
    print("Expected: minimum when s1 = s2 (both +1 or both -1)")
    
    # Compile to QUBO (automatic Ising to QUBO conversion)
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"QUBO matrix (after Ising→QUBO conversion):\n{matrix}")
    print(f"Variables: {list(index_map.keys())}")
    print(f"Offset: {offset}")
    
    # Test with different QAIA algorithms
    algorithms = ['asb', 'bsb', 'dsb']
    
    for algorithm in algorithms:
        print(f"\n--- Testing {algorithm.upper()} with Ising symbols ---")
        try:
            sampler = QAIASampler(algorithm=algorithm, seed=42, backend='cpu-float32')
            result = sampler.run(qubo, shots=10, n_iter=50, dt=0.1, xi=0.1)
            
            best_solution = result[0][0]
            best_energy = result[0][1]
            
            print(f"Best solution: {best_solution}")
            print(f"Best energy: {best_energy}")
            
            # Note: The solution will be in binary format (0,1) but represents Ising variables
            # We need to convert back to Ising to check the constraint
            s1_ising = 2 * best_solution['s1_bin'] - 1  # Convert 0,1 → -1,+1
            s2_ising = 2 * best_solution['s2_bin'] - 1
            
            print(f"Ising interpretation: s1={s1_ising}, s2={s2_ising}")
            
            # Verify constraint: s1 should equal s2
            constraint_satisfied = (s1_ising == s2_ising)
            print(f"Constraint (s1 = s2): {s1_ising} = {s2_ising} {'✓' if constraint_satisfied else '✗'}")
            
            # Verify binary values in output
            for var, val in best_solution.items():
                if val not in [0, 1]:
                    print(f"❌ Non-binary value found: {var} = {val}")
                    return False
            
            print(f"✅ {algorithm.upper()} with Ising symbols: SUCCESS")
            
        except Exception as e:
            print(f"❌ {algorithm.upper()} with Ising symbols: FAILED - {e}")
            return False
    
    return True


def test_mixed_symbols():
    """Test QAIASampler with mixed binary and Ising symbols."""
    print("\n\n=== Testing Mixed Symbols ===")
    
    clear_symbol_registry()
    
    # Create mixed symbols
    x = symbols('x', symbol_type="binary")   # Binary variable
    s = symbols('s', symbol_type="ising")    # Ising variable
    
    print(f"Created mixed symbols: x (binary), s (ising)")
    
    # Mixed expression: coupling between binary and Ising
    expr = x * s + x + s
    print(f"Expression: x * s + x + s")
    print("This couples a binary decision variable with an Ising spin")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"QUBO matrix (after mixed→QUBO conversion):\n{matrix}")
    print(f"Variables: {list(index_map.keys())}")
    print(f"Offset: {offset}")
    
    # Test with ASB algorithm
    print(f"\n--- Testing ASB with mixed symbols ---")
    try:
        sampler = QAIASampler(algorithm='asb', seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=10, n_iter=50, dt=0.1, xi=0.1)
        
        best_solution = result[0][0]
        best_energy = result[0][1]
        
        print(f"Best solution: {best_solution}")
        print(f"Best energy: {best_energy}")
        
        # Verify binary values in output
        for var, val in best_solution.items():
            if val not in [0, 1]:
                print(f"❌ Non-binary value found: {var} = {val}")
                return False
        
        print(f"✅ Mixed symbols: SUCCESS")
        
    except Exception as e:
        print(f"❌ Mixed symbols: FAILED - {e}")
        return False
    
    return True


def test_larger_ising_problem():
    """Test QAIASampler with a larger Ising problem."""
    print("\n\n=== Testing Larger Ising Problem ===")
    
    clear_symbol_registry()
    
    # Create 4 Ising spins
    s1, s2, s3, s4 = symbols('s1 s2 s3 s4', symbol_type="ising")
    print(f"Created 4 Ising spins: s1, s2, s3, s4")
    
    # Ising model: ferromagnetic interactions (spins want to align)
    # Energy = -J * (s1*s2 + s2*s3 + s3*s4 + s4*s1)  [minimize = maximize alignment]
    J = 1.0  # Coupling strength
    expr = -J * (s1*s2 + s2*s3 + s3*s4 + s4*s1)
    
    print(f"Expression: -J * (s1*s2 + s2*s3 + s3*s4 + s4*s1) with J={J}")
    print("Ferromagnetic model: spins prefer to align (all +1 or all -1)")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"QUBO matrix shape: {matrix.shape}")
    print(f"Variables: {list(index_map.keys())}")
    print(f"Offset: {offset}")
    
    # Test with BSB algorithm (good for this type of problem)
    print(f"\n--- Testing BSB with larger Ising problem ---")
    try:
        sampler = QAIASampler(algorithm='bsb', seed=42, backend='cpu-float32')
        result = sampler.run(qubo, shots=20, n_iter=100, dt=0.1, xi=0.1)
        
        best_solution = result[0][0]
        best_energy = result[0][1]
        
        print(f"Best solution: {best_solution}")
        print(f"Best energy: {best_energy}")
        
        # Convert to Ising interpretation
        ising_values = {}
        for var, val in best_solution.items():
            if var.endswith('_bin'):
                original_var = var.replace('_bin', '')
                ising_values[original_var] = 2 * val - 1
        
        print(f"Ising interpretation: {ising_values}")
        
        # Check if spins are aligned (all same value)
        ising_vals = list(ising_values.values())
        all_aligned = len(set(ising_vals)) == 1
        print(f"All spins aligned: {'✓' if all_aligned else '✗'}")
        
        print(f"✅ Larger Ising problem: SUCCESS")
        
    except Exception as e:
        print(f"❌ Larger Ising problem: FAILED - {e}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("🧪 QAIASampler Symbol Type Testing")
    print("=" * 50)
    
    tests = [
        ("Binary Symbols", test_binary_symbols),
        ("Ising Symbols", test_ising_symbols),
        ("Mixed Symbols", test_mixed_symbols),
        ("Larger Ising Problem", test_larger_ising_problem),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} test...")
        try:
            success = test_func()
            results[test_name] = success
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\nQAIASampler successfully works with:")
        print("✅ Binary symbols (traditional QUBO)")
        print("✅ Ising symbols (automatic conversion)")
        print("✅ Mixed symbol types")
        print("✅ Larger Ising problems")
        print("\nThe sampler correctly:")
        print("- Converts Ising symbols to QUBO format")
        print("- Applies QAIA algorithms")
        print("- Returns results in consistent binary format")
        print("- Maintains mathematical correctness")
    else:
        print("❌ SOME TESTS FAILED")
        print("Please check the error messages above.")


if __name__ == "__main__":
    main()
