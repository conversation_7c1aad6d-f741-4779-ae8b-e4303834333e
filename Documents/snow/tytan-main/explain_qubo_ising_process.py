#!/usr/bin/env python3
"""
Comprehensive explanation of the QUBO to Is<PERSON> conversion process.

This script demonstrates:
1. How to generate QUBO from expression
2. What the offset means
3. How to transfer QUBO to Ising
4. How to process the Ising offset along with QUBO offset
"""

import sys
sys.path.insert(0, '/Users/<USER>/Documents/snow/tytan-main')

import numpy as np
from tytan import symbols, Compile, clear_symbol_registry
from tytan.qaia_sampler import QAIASampler


def step1_expression_to_qubo():
    """
    Step 1: How to generate QUBO from expression
    """
    print("=" * 60)
    print("STEP 1: EXPRESSION TO QUBO CONVERSION")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    
    # Example expression: (x + y + z - 2)^2
    expr = (x + y + z - 2)**2
    print(f"Original expression: {expr}")
    print("This represents: minimize the penalty for not having exactly 2 variables equal to 1")
    
    # Manual expansion to understand the process
    print("\n📝 Manual expansion:")
    print("(x + y + z - 2)² = x² + y² + z² + 2xy + 2xz + 2yz - 4x - 4y - 4z + 4")
    print("For binary variables: x² = x, y² = y, z² = z")
    print("So: x + y + z + 2xy + 2xz + 2yz - 4x - 4y - 4z + 4")
    print("   = -3x - 3y - 3z + 2xy + 2xz + 2yz + 4")
    
    # Compile to QUBO
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"\n🔢 QUBO Matrix:")
    print(f"Matrix:\n{matrix}")
    print(f"Index mapping: {index_map}")
    print(f"Offset: {offset}")
    
    print(f"\n📊 QUBO Matrix interpretation:")
    print(f"- Diagonal elements: linear coefficients")
    print(f"  Q[0,0] = {matrix[0,0]} (coefficient of x)")
    print(f"  Q[1,1] = {matrix[1,1]} (coefficient of y)")
    print(f"  Q[2,2] = {matrix[2,2]} (coefficient of z)")
    print(f"- Off-diagonal elements: quadratic coefficients")
    print(f"  Q[0,1] = {matrix[0,1]} (coefficient of xy)")
    print(f"  Q[0,2] = {matrix[0,2]} (coefficient of xz)")
    print(f"  Q[1,2] = {matrix[1,2]} (coefficient of yz)")
    
    return matrix, index_map, offset


def step2_understand_offset():
    """
    Step 2: What the offset means
    """
    print("\n" + "=" * 60)
    print("STEP 2: UNDERSTANDING THE OFFSET")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"🎯 The offset represents the constant term in the expression")
    print(f"Original expression: (x + y + z - 2)²")
    print(f"Expanded: -3x - 3y - 3z + 2xy + 2xz + 2yz + 4")
    print(f"                                                    ↑")
    print(f"                                            constant term = {offset}")
    
    print(f"\n🧮 Energy calculation:")
    print(f"Total energy = x^T Q x + offset")
    print(f"             = QUBO matrix energy + constant term")
    
    # Test with a specific solution
    test_solution = np.array([1, 1, 0])  # x=1, y=1, z=0
    qubo_energy = test_solution.T @ matrix @ test_solution
    total_energy = qubo_energy + offset
    manual_energy = (1 + 1 + 0 - 2)**2
    
    print(f"\n📋 Example with solution [x=1, y=1, z=0]:")
    print(f"QUBO matrix energy: {qubo_energy}")
    print(f"Offset:            {offset}")
    print(f"Total energy:      {total_energy}")
    print(f"Manual calculation: (1+1+0-2)² = {manual_energy}")
    print(f"Match: {total_energy == manual_energy} ✓")
    
    return offset


def step3_qubo_to_ising():
    """
    Step 3: How to transfer QUBO to Ising
    """
    print("\n" + "=" * 60)
    print("STEP 3: QUBO TO ISING CONVERSION")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    qubo, offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    print(f"🔄 Conversion process:")
    print(f"QUBO: minimize x^T Q x where x ∈ {{0,1}}^n")
    print(f"Ising: minimize s^T J s + h^T s + constant where s ∈ {{-1,+1}}^n")
    print(f"Transformation: x_i = (s_i + 1)/2")
    
    # Apply the conversion
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"\n📊 Conversion results:")
    print(f"J matrix (interactions):\n{J}")
    print(f"h vector (linear terms): {h}")
    print(f"Ising offset: {ising_offset}")
    
    print(f"\n🔍 Conversion formula details:")
    print(f"For QUBO matrix Q, the Ising parameters are:")
    print(f"- J[i,j] = Q[i,j] / 4 for i ≠ j")
    print(f"- h[i] = Q[i,i] / 2 + Σ_j≠i Q[i,j] / 4")
    print(f"- constant = 0.5 * Σ_i Q[i,i] + 0.25 * Σ_i≠j Q[i,j]")
    
    # Verify the conversion
    print(f"\n✅ Verification:")
    test_solutions = [[0,0,0], [1,1,0], [0,1,1]]
    
    for binary_sol in test_solutions:
        binary_sol = np.array(binary_sol)
        ising_sol = 2 * binary_sol - 1
        
        qubo_energy = binary_sol.T @ matrix @ binary_sol
        ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + ising_offset
        
        match = abs(qubo_energy - ising_energy) < 1e-10
        print(f"Solution {list(binary_sol)}: QUBO={qubo_energy:5.1f}, Ising={ising_energy:5.1f}, Match={'✓' if match else '✗'}")
    
    return J, h, ising_offset


def step4_offset_handling():
    """
    Step 4: How to process the Ising offset along with QUBO offset
    """
    print("\n" + "=" * 60)
    print("STEP 4: OFFSET HANDLING IN THE COMPLETE PROCESS")
    print("=" * 60)
    
    clear_symbol_registry()
    x, y, z = symbols('x y z')
    expr = (x + y + z - 2)**2
    
    qubo, qubo_offset = Compile(expr).get_qubo()
    matrix, index_map = qubo
    
    # Get Ising conversion
    sampler = QAIASampler()
    J, h, ising_index_map, ising_offset = sampler._qubo_to_ising(matrix, index_map)
    
    print(f"📋 Complete energy accounting:")
    print(f"Original expression: (x + y + z - 2)²")
    print(f"QUBO offset:    {qubo_offset}")
    print(f"Ising offset:   {ising_offset}")
    print(f"Total offset:   {qubo_offset + ising_offset}")
    
    print(f"\n🔄 Energy flow:")
    print(f"1. Original expression energy = (x + y + z - 2)²")
    print(f"2. QUBO energy = x^T Q x + qubo_offset")
    print(f"3. Ising energy = s^T J s + h^T s + ising_offset")
    print(f"4. Final energy = Ising energy + qubo_offset")
    
    # Test with all possible solutions
    print(f"\n🧪 Complete verification:")
    print(f"Binary -> Ising -> Original | QUBO+offset | Ising+offset | Final")
    print(f"-" * 70)
    
    for binary_sol in [[0,0,0], [0,0,1], [0,1,0], [0,1,1], [1,0,0], [1,0,1], [1,1,0], [1,1,1]]:
        binary_sol = np.array(binary_sol)
        ising_sol = 2 * binary_sol - 1
        
        # Calculate energies at each stage
        original_energy = (sum(binary_sol) - 2)**2
        qubo_matrix_energy = binary_sol.T @ matrix @ binary_sol
        qubo_total_energy = qubo_matrix_energy + qubo_offset
        ising_energy = ising_sol.T @ J @ ising_sol + h.T @ ising_sol + ising_offset
        final_energy = ising_energy + qubo_offset
        
        print(f"{list(binary_sol)} -> {list(ising_sol)} -> {original_energy:4.0f} | "
              f"{qubo_total_energy:8.1f} | {ising_energy:8.1f} | {final_energy:8.1f}")
    
    print(f"\n💡 Key insights:")
    print(f"1. QUBO offset comes from the constant term in the original expression")
    print(f"2. Ising offset comes from the QUBO→Ising mathematical transformation")
    print(f"3. In practice, we often ignore offsets for optimization (they don't affect ranking)")
    print(f"4. For energy calculations, we need: Ising_energy + QUBO_offset")
    
    print(f"\n⚙️  In the QAIASampler implementation:")
    print(f"- The QAIA algorithm minimizes: s^T J s + h^T s")
    print(f"- We convert solutions back to binary: x = (s + 1)/2")
    print(f"- We calculate final energies using: x^T Q x (QUBO matrix energy)")
    print(f"- The get_result() function handles the final formatting")


def main():
    """Run the complete explanation."""
    print("🎓 COMPLETE QUBO TO ISING CONVERSION PROCESS")
    print("Understanding the mathematical transformation and offset handling")
    
    try:
        step1_expression_to_qubo()
        step2_understand_offset()
        step3_qubo_to_ising()
        step4_offset_handling()
        
        print("\n" + "=" * 60)
        print("✅ SUMMARY")
        print("=" * 60)
        print("1. Expression → QUBO: Compile() expands and extracts coefficients")
        print("2. QUBO offset: Constant term from original expression")
        print("3. QUBO → Ising: Mathematical transformation x = (s+1)/2")
        print("4. Ising offset: Additional constant from the transformation")
        print("5. Final energy: Use QUBO matrix energy for consistent results")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")


if __name__ == "__main__":
    main()
